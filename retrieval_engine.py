"""
检索匹配引擎
实现双路召回（稠密+稀疏）的检索匹配系统
"""

from typing import List, Dict, Tuple, Any, Optional
import numpy as np
from dataclasses import dataclass

from data_models import SearchResult, ExtractedPaperData, DataManager
from semantic_space import SemanticSpaceManager


@dataclass
class RetrievalConfig:
    """检索配置"""
    dense_weight: float = 0.6  # 稠密检索权重
    sparse_weight: float = 0.4  # 稀疏检索权重
    top_k_per_space: int = 10  # 每个空间返回的top-k结果
    final_top_k: int = 5  # 最终返回的top-k结果
    min_score_threshold: float = 0.2  # 最小分数阈值（调整到0.2）
    use_original_scores: bool = False  # 是否使用原始分数而不是标准化分数


class RetrievalEngine:
    """检索引擎"""
    
    def __init__(self, config: RetrievalConfig = None, vector_db_dir: str = "./vector_db", 
                 data_dir: str = "./data"):
        self.config = config or RetrievalConfig()
        self.semantic_manager = SemanticSpaceManager(vector_db_dir)
        self.data_manager = DataManager(data_dir)
        self.papers_cache = {}  # 缓存论文数据
    
    def initialize(self) -> bool:
        """初始化检索引擎"""
        # 尝试加载已有的语义空间
        if self.semantic_manager.load_all():
            print("检索引擎初始化成功")
            return True
        else:
            print("未找到预构建的语义空间，需要先构建")
            return False
    
    def build_semantic_spaces(self, papers: List[ExtractedPaperData] = None):
        """构建语义空间"""
        if papers is None:
            # 加载所有论文数据
            papers = self.data_manager.load_all_papers()
        
        if not papers:
            print("没有找到论文数据")
            return
        
        # 构建语义空间
        self.semantic_manager.build_all_spaces(papers)
        
        # 保存语义空间
        self.semantic_manager.save_all()
        
        print("语义空间构建并保存完成")
    
    def _normalize_scores(self, scores: List[float]) -> List[float]:
        """标准化分数到[0,1]区间"""
        if not scores:
            return scores

        min_score = min(scores)
        max_score = max(scores)

        # 如果所有分数相同，返回原始分数而不是全部设为1.0
        if max_score == min_score:
            return scores

        return [(score - min_score) / (max_score - min_score) for score in scores]

    def _calculate_relevance_score(self, dense_score: float, sparse_score: float,
                                  original_dense: float, original_sparse: float) -> float:
        """计算相关性分数，结合标准化分数和原始分数"""
        if self.config.use_original_scores:
            # 使用原始分数的加权平均
            final_score = (self.config.dense_weight * original_dense +
                          self.config.sparse_weight * original_sparse)
        else:
            # 使用原始分数，但进行适当的缩放
            # 稠密检索分数通常在0-1之间，稀疏检索分数可能更大
            scaled_dense = min(original_dense, 1.0)  # 限制在1.0以内
            scaled_sparse = min(original_sparse / 10.0, 1.0)  # BM25分数通常较大，需要缩放

            final_score = (self.config.dense_weight * scaled_dense +
                          self.config.sparse_weight * scaled_sparse)

            # 如果两个分数都很低，进一步降低最终分数
            if scaled_dense < 0.3 and scaled_sparse < 0.1:
                final_score *= 0.3

        return final_score
    
    def _merge_results(self, dense_results: List[Tuple], sparse_results: List[Tuple],
                      search_type: str) -> List[SearchResult]:
        """合并稠密和稀疏检索结果"""
        # 创建结果字典，以item_id为键
        merged_results = {}

        # 保存原始分数
        dense_scores = [result[1] for result in dense_results]
        sparse_scores = [result[1] for result in sparse_results]

        # 标准化分数
        normalized_dense_scores = self._normalize_scores(dense_scores)
        normalized_sparse_scores = self._normalize_scores(sparse_scores)

        # 处理稠密检索结果
        for i, (item, original_score, paper_id) in enumerate(dense_results):
            item_id = item.problem_id if search_type == 'problem' else item.method_id
            normalized_score = normalized_dense_scores[i]

            if item_id not in merged_results:
                merged_results[item_id] = {
                    'item': item,
                    'paper_id': paper_id,
                    'dense_score': normalized_score,
                    'sparse_score': 0.0,
                    'original_dense': original_score,
                    'original_sparse': 0.0
                }
            else:
                # 取最高分数
                if original_score > merged_results[item_id]['original_dense']:
                    merged_results[item_id]['dense_score'] = normalized_score
                    merged_results[item_id]['original_dense'] = original_score

        # 处理稀疏检索结果
        for i, (item, original_score, paper_id) in enumerate(sparse_results):
            item_id = item.problem_id if search_type == 'problem' else item.method_id
            normalized_score = normalized_sparse_scores[i]

            if item_id not in merged_results:
                merged_results[item_id] = {
                    'item': item,
                    'paper_id': paper_id,
                    'dense_score': 0.0,
                    'sparse_score': normalized_score,
                    'original_dense': 0.0,
                    'original_sparse': original_score
                }
            else:
                # 取最高分数
                if original_score > merged_results[item_id]['original_sparse']:
                    merged_results[item_id]['sparse_score'] = normalized_score
                    merged_results[item_id]['original_sparse'] = original_score

        # 计算最终分数并创建SearchResult对象
        search_results = []
        for item_id, result_data in merged_results.items():
            # 使用新的相关性计算方法
            final_score = self._calculate_relevance_score(
                result_data['dense_score'],
                result_data['sparse_score'],
                result_data['original_dense'],
                result_data['original_sparse']
            )

            # 过滤低分结果
            if final_score >= self.config.min_score_threshold:
                item = result_data['item']

                # 获取内容文本
                if search_type == 'problem':
                    content = f"{item.title}: {item.description}"
                else:
                    content = f"{item.name}: {item.description}"

                # 获取论文标题
                paper_title = self._get_paper_title(result_data['paper_id'])

                search_result = SearchResult(
                    item_id=item_id,
                    item_type=search_type,
                    content=content,
                    score=final_score,
                    paper_id=result_data['paper_id'],
                    paper_title=paper_title
                )
                search_results.append(search_result)

        # 按分数排序并返回top-k结果
        search_results.sort(key=lambda x: x.score, reverse=True)
        return search_results[:self.config.final_top_k]
    
    def _get_paper_title(self, paper_id: str) -> str:
        """获取论文标题（带缓存）"""
        if paper_id not in self.papers_cache:
            paper_data = self.data_manager.load_paper_data(paper_id)
            if paper_data:
                self.papers_cache[paper_id] = paper_data.paper_info.title
            else:
                self.papers_cache[paper_id] = "未知论文"
        
        return self.papers_cache[paper_id]
    
    def search_problems(self, query: str) -> List[SearchResult]:
        """搜索研究问题"""
        # 稠密检索
        dense_results = self.semantic_manager.problem_dense.search(
            query, self.config.top_k_per_space
        )
        
        # 稀疏检索
        sparse_results = self.semantic_manager.problem_sparse.search(
            query, self.config.top_k_per_space
        )
        
        # 合并结果
        return self._merge_results(dense_results, sparse_results, 'problem')
    
    def search_methods(self, query: str) -> List[SearchResult]:
        """搜索研究方法"""
        # 稠密检索
        dense_results = self.semantic_manager.method_dense.search(
            query, self.config.top_k_per_space
        )
        
        # 稀疏检索
        sparse_results = self.semantic_manager.method_sparse.search(
            query, self.config.top_k_per_space
        )
        
        # 合并结果
        return self._merge_results(dense_results, sparse_results, 'method')
    
    def search_all(self, query: str) -> Dict[str, List[SearchResult]]:
        """同时搜索问题和方法"""
        return {
            'problems': self.search_problems(query),
            'methods': self.search_methods(query)
        }
    
    def get_paper_content(self, paper_id: str) -> Optional[ExtractedPaperData]:
        """获取完整的论文内容"""
        return self.data_manager.load_paper_data(paper_id)
    
    def get_related_items(self, item_id: str, item_type: str, top_k: int = 3) -> List[SearchResult]:
        """获取相关的项目（基于同一论文或相似内容）"""
        # 这里可以实现更复杂的相关性逻辑
        # 简单实现：返回来自同一论文的其他项目
        results = []
        
        # 首先找到该项目所属的论文
        target_paper_id = None
        all_papers = self.data_manager.load_all_papers()
        
        for paper in all_papers:
            if item_type == 'problem':
                for problem in paper.research_problems:
                    if problem.problem_id == item_id:
                        target_paper_id = paper.paper_info.paper_id
                        break
            else:
                for method in paper.research_methods:
                    if method.method_id == item_id:
                        target_paper_id = paper.paper_info.paper_id
                        break
            
            if target_paper_id:
                break
        
        if target_paper_id:
            # 返回同一论文中的其他项目
            target_paper = self.data_manager.load_paper_data(target_paper_id)
            if target_paper:
                # 添加同论文的其他类型项目
                if item_type == 'problem':
                    for method in target_paper.research_methods:
                        content = f"{method.name}: {method.description}"
                        result = SearchResult(
                            item_id=method.method_id,
                            item_type='method',
                            content=content,
                            score=1.0,  # 同论文相关性设为1.0
                            paper_id=target_paper_id,
                            paper_title=target_paper.paper_info.title
                        )
                        results.append(result)
                else:
                    for problem in target_paper.research_problems:
                        content = f"{problem.title}: {problem.description}"
                        result = SearchResult(
                            item_id=problem.problem_id,
                            item_type='problem',
                            content=content,
                            score=1.0,
                            paper_id=target_paper_id,
                            paper_title=target_paper.paper_info.title
                        )
                        results.append(result)
        
        return results[:top_k]
