"""
命令行测试工具
验证检索功能修复效果
"""

from retrieval_engine import RetrievalEngine, RetrievalConfig
from data_models import DataManager
import os


def test_retrieval_relevance():
    """测试检索相关性"""
    print("🔍 测试检索相关性...")
    
    # 初始化检索引擎
    engine = RetrievalEngine()
    if not engine.initialize():
        print("❌ 检索引擎初始化失败")
        return False
    
    # 测试查询和期望结果
    test_cases = [
        {
            "query": "计算机视觉",
            "expected_keywords": ["视觉", "图像", "CNN", "深度学习"],
            "unexpected_keywords": ["游戏化", "移动学习", "虚拟现实"]
        },
        {
            "query": "自然语言处理",
            "expected_keywords": ["NLP", "文本", "BERT", "Transformer"],
            "unexpected_keywords": ["视觉", "VR", "游戏"]
        },
        {
            "query": "游戏化学习",
            "expected_keywords": ["游戏", "积分", "徽章", "动机"],
            "unexpected_keywords": ["视觉", "NLP", "知识图谱"]
        },
        {
            "query": "移动学习",
            "expected_keywords": ["移动", "碎片化", "知识整合"],
            "unexpected_keywords": ["VR", "游戏化", "计算机视觉"]
        },
        {
            "query": "虚拟现实教育",
            "expected_keywords": ["VR", "虚拟", "沉浸", "3D"],
            "unexpected_keywords": ["移动", "游戏化", "NLP"]
        }
    ]
    
    total_tests = len(test_cases)
    passed_tests = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}/{total_tests}: '{test_case['query']}'")
        
        # 搜索问题和方法
        results = engine.search_all(test_case['query'])
        problems = results.get('problems', [])
        methods = results.get('methods', [])
        
        print(f"  找到 {len(problems)} 个问题，{len(methods)} 个方法")
        
        # 检查结果相关性
        all_results = problems + methods
        if not all_results:
            print("  ❌ 没有找到任何结果")
            continue
        
        # 显示前3个结果
        print("  前3个结果:")
        for j, result in enumerate(all_results[:3], 1):
            print(f"    {j}. [{result.item_type}] {result.content[:80]}... (分数: {result.score:.3f})")
        
        # 检查相关性
        relevant_found = False
        irrelevant_found = False
        
        for result in all_results:
            content_lower = result.content.lower()
            
            # 检查是否包含期望的关键词
            for keyword in test_case['expected_keywords']:
                if keyword.lower() in content_lower:
                    relevant_found = True
                    break
            
            # 检查是否包含不期望的关键词
            for keyword in test_case['unexpected_keywords']:
                if keyword.lower() in content_lower:
                    irrelevant_found = True
                    break
        
        # 评估测试结果
        if relevant_found and not irrelevant_found:
            print("  ✅ 相关性测试通过")
            passed_tests += 1
        elif relevant_found and irrelevant_found:
            print("  ⚠️ 部分通过（找到相关结果但也有不相关结果）")
            passed_tests += 0.5
        else:
            print("  ❌ 相关性测试失败")
    
    success_rate = passed_tests / total_tests
    print(f"\n📊 测试结果：{passed_tests}/{total_tests} 通过，成功率: {success_rate:.1%}")
    
    return success_rate >= 0.7  # 70%通过率认为成功


def test_score_filtering():
    """测试分数过滤功能"""
    print("\n🎯 测试分数过滤功能...")
    
    # 测试不同的配置
    configs = [
        {"min_score_threshold": 0.1, "name": "低阈值(0.1)"},
        {"min_score_threshold": 0.3, "name": "中阈值(0.3)"},
        {"min_score_threshold": 0.5, "name": "高阈值(0.5)"},
    ]
    
    for config_data in configs:
        print(f"\n测试配置: {config_data['name']}")
        
        config = RetrievalConfig(min_score_threshold=config_data['min_score_threshold'])
        engine = RetrievalEngine(config=config)
        engine.initialize()
        
        # 使用一个通用查询
        results = engine.search_all("学习")
        problems = results.get('problems', [])
        methods = results.get('methods', [])
        
        print(f"  找到 {len(problems)} 个问题，{len(methods)} 个方法")
        
        if problems or methods:
            all_results = problems + methods
            min_score = min(r.score for r in all_results)
            max_score = max(r.score for r in all_results)
            avg_score = sum(r.score for r in all_results) / len(all_results)
            
            print(f"  分数范围: {min_score:.3f} - {max_score:.3f}, 平均: {avg_score:.3f}")
        else:
            print("  没有找到结果")
    
    return True


def test_configuration_effects():
    """测试配置参数的影响"""
    print("\n⚙️ 测试配置参数影响...")
    
    # 测试不同的top_k设置
    top_k_values = [2, 5, 10]
    
    for top_k in top_k_values:
        print(f"\n测试 top_k = {top_k}")
        
        config = RetrievalConfig(final_top_k=top_k)
        engine = RetrievalEngine(config=config)
        engine.initialize()
        
        results = engine.search_all("深度学习")
        problems = results.get('problems', [])
        methods = results.get('methods', [])
        
        print(f"  问题结果数: {len(problems)}, 方法结果数: {len(methods)}")
        
        # 验证结果数量不超过配置的top_k
        if len(problems) <= top_k and len(methods) <= top_k:
            print("  ✅ top_k 配置生效")
        else:
            print("  ❌ top_k 配置未生效")
    
    return True


def test_diverse_queries():
    """测试多样化查询"""
    print("\n🌈 测试多样化查询...")
    
    engine = RetrievalEngine()
    engine.initialize()
    
    # 不同类型的查询
    queries = [
        "深度学习",  # 技术术语
        "学习效果评估",  # 教育概念
        "用户体验设计",  # 设计方法
        "知识图谱构建",  # 具体技术
        "沉浸式体验",  # 体验描述
        "不存在的概念xyz123"  # 无关查询
    ]
    
    for query in queries:
        print(f"\n查询: '{query}'")
        
        results = engine.search_all(query)
        problems = results.get('problems', [])
        methods = results.get('methods', [])
        
        total_results = len(problems) + len(methods)
        print(f"  结果数: {total_results}")
        
        if total_results > 0:
            all_results = problems + methods
            avg_score = sum(r.score for r in all_results) / len(all_results)
            print(f"  平均分数: {avg_score:.3f}")
            
            # 显示最佳结果
            best_result = max(all_results, key=lambda x: x.score)
            print(f"  最佳匹配: [{best_result.item_type}] {best_result.content[:60]}... (分数: {best_result.score:.3f})")
        else:
            print("  没有找到相关结果")
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始检索功能修复验证...\n")
    
    # 检查数据
    data_manager = DataManager("./data")
    papers = data_manager.load_all_papers()
    print(f"📚 加载了 {len(papers)} 篇论文")
    
    total_problems = sum(len(paper.research_problems) for paper in papers)
    total_methods = sum(len(paper.research_methods) for paper in papers)
    print(f"📊 总计：{total_problems} 个研究问题，{total_methods} 个研究方法\n")
    
    # 运行测试
    tests = [
        ("检索相关性测试", test_retrieval_relevance),
        ("分数过滤测试", test_score_filtering),
        ("配置参数测试", test_configuration_effects),
        ("多样化查询测试", test_diverse_queries)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"{'='*60}")
        print(f"运行: {test_name}")
        print(f"{'='*60}")
        
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 出错: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 总体测试结果: {passed_tests}/{total_tests} 通过")
    print(f"成功率: {passed_tests/total_tests:.1%}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！检索功能修复成功。")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步优化。")
        return False


if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 测试过程中发生错误：{e}")
        import traceback
        traceback.print_exc()
        exit(1)
