"""
创建更多样化的测试数据
用于验证检索功能的准确性和相关性
"""

from data_models import *
from datetime import datetime
import os


def create_diverse_papers():
    """创建更多样化的论文数据"""
    
    papers = []
    
    # 论文1：计算机视觉在教育中的应用
    paper1 = ExtractedPaperData(
        paper_info=PaperInfo(
            paper_id="CV001",
            title="基于计算机视觉的在线学习行为识别与分析",
            authors=["陈明", "李华", "王强"],
            year=2023,
            venue="计算机视觉与模式识别",
            abstract="本研究提出了一种基于计算机视觉技术的在线学习行为自动识别方法，通过分析学习者的面部表情、眼动轨迹和手势动作，实现对学习状态的实时监测和评估。",
            keywords=["计算机视觉", "行为识别", "在线学习", "学习状态监测"],
            doi="10.1234/cv.2023.001"
        ),
        research_problems=[
            ResearchProblem(
                problem_id="P101",
                title="如何通过计算机视觉技术识别学习者的注意力状态",
                description="在线学习环境中，学习者的注意力状态难以直接观察。如何利用计算机视觉技术，通过分析面部表情、眼动模式等视觉特征，准确识别学习者的注意力集中程度，是提高在线教学效果的关键问题。",
                keywords=["计算机视觉", "注意力识别", "面部表情分析"],
                domain="计算机科学",
                sub_domain="计算机视觉",
                research_type="技术研究"
            )
        ],
        research_methods=[
            ResearchMethod(
                method_id="M101",
                name="深度卷积神经网络",
                description="使用深度卷积神经网络进行面部表情和眼动模式的特征提取和分类",
                category="定量方法",
                techniques=["CNN", "ResNet", "LSTM", "注意力机制"],
                data_types=["视频数据", "图像数据", "眼动数据"],
                analysis_tools=["TensorFlow", "OpenCV", "PyTorch"]
            )
        ],
        full_text="这里是计算机视觉论文的完整文本...",
        extraction_date=datetime.now().isoformat()
    )
    
    # 论文2：自然语言处理在教育评估中的应用
    paper2 = ExtractedPaperData(
        paper_info=PaperInfo(
            paper_id="NLP001",
            title="基于自然语言处理的学生作文自动评分系统",
            authors=["张伟", "刘芳", "赵敏"],
            year=2023,
            venue="自然语言处理学报",
            abstract="本研究开发了一个基于深度学习的学生作文自动评分系统，通过分析文本的语法、语义、逻辑结构等多维度特征，实现对学生写作能力的客观评估。",
            keywords=["自然语言处理", "自动评分", "文本分析", "深度学习"],
            doi="10.1234/nlp.2023.001"
        ),
        research_problems=[
            ResearchProblem(
                problem_id="P201",
                title="如何构建准确的中文作文自动评分模型",
                description="中文作文的评分涉及语法、语义、逻辑、创意等多个维度，传统的自动评分方法难以准确捕捉这些复杂特征。如何构建一个能够综合考虑多维度特征的中文作文自动评分模型，是当前面临的挑战。",
                keywords=["自动评分", "中文文本处理", "多维度评估"],
                domain="计算机科学",
                sub_domain="自然语言处理",
                research_type="应用研究"
            )
        ],
        research_methods=[
            ResearchMethod(
                method_id="M201",
                name="Transformer模型",
                description="使用预训练的Transformer模型进行文本特征提取和评分预测",
                category="定量方法",
                techniques=["BERT", "GPT", "注意力机制", "迁移学习"],
                data_types=["文本数据", "评分数据", "语料库"],
                analysis_tools=["Transformers", "PyTorch", "NLTK"]
            )
        ],
        full_text="这里是自然语言处理论文的完整文本...",
        extraction_date=datetime.now().isoformat()
    )
    
    # 论文3：游戏化学习平台设计
    paper3 = ExtractedPaperData(
        paper_info=PaperInfo(
            paper_id="GAME001",
            title="基于游戏化机制的编程学习平台设计与实现",
            authors=["杨帆", "马超", "林静"],
            year=2022,
            venue="教育游戏研究",
            abstract="本研究设计并实现了一个融合游戏化元素的编程学习平台，通过积分、徽章、排行榜等机制激发学习者的学习动机，提高编程学习的趣味性和有效性。",
            keywords=["游戏化学习", "编程教育", "学习动机", "平台设计"],
            doi="10.1234/game.2022.001"
        ),
        research_problems=[
            ResearchProblem(
                problem_id="P301",
                title="游戏化元素如何有效提升编程学习效果",
                description="编程学习往往枯燥乏味，学习者容易失去兴趣。如何设计合适的游戏化元素，在不影响学习内容严肃性的前提下，有效提升学习者的参与度和学习效果，是游戏化教育面临的核心问题。",
                keywords=["游戏化设计", "学习动机", "编程教育"],
                domain="教育技术",
                sub_domain="游戏化学习",
                research_type="设计研究"
            )
        ],
        research_methods=[
            ResearchMethod(
                method_id="M301",
                name="用户体验设计方法",
                description="采用以用户为中心的设计方法，通过用户调研、原型设计、可用性测试等步骤优化平台设计",
                category="定性方法",
                techniques=["用户调研", "原型设计", "A/B测试", "可用性测试"],
                data_types=["用户行为数据", "问卷数据", "访谈数据"],
                analysis_tools=["Figma", "Unity", "Google Analytics"]
            )
        ],
        full_text="这里是游戏化学习论文的完整文本...",
        extraction_date=datetime.now().isoformat()
    )
    
    # 论文4：移动学习应用研究
    paper4 = ExtractedPaperData(
        paper_info=PaperInfo(
            paper_id="MOBILE001",
            title="移动学习环境下的碎片化知识整合策略研究",
            authors=["周涛", "许丽", "胡斌"],
            year=2023,
            venue="移动计算与教育",
            abstract="本研究针对移动学习中知识碎片化的问题，提出了一种基于知识图谱的碎片化知识整合策略，帮助学习者构建完整的知识体系。",
            keywords=["移动学习", "知识整合", "碎片化学习", "知识图谱"],
            doi="10.1234/mobile.2023.001"
        ),
        research_problems=[
            ResearchProblem(
                problem_id="P401",
                title="移动学习中碎片化知识的有效整合方法",
                description="移动学习具有时间碎片化、内容碎片化的特点，学习者难以形成系统性的知识结构。如何设计有效的知识整合机制，帮助学习者将碎片化的学习内容组织成完整的知识体系，是移动学习面临的重要挑战。",
                keywords=["移动学习", "知识整合", "碎片化学习"],
                domain="教育技术",
                sub_domain="移动学习",
                research_type="理论研究"
            )
        ],
        research_methods=[
            ResearchMethod(
                method_id="M401",
                name="知识图谱构建与推理",
                description="构建领域知识图谱，通过图推理算法发现知识间的关联关系",
                category="定量方法",
                techniques=["知识抽取", "实体链接", "关系推理", "图神经网络"],
                data_types=["文本数据", "知识库", "学习轨迹数据"],
                analysis_tools=["Neo4j", "PyTorch Geometric", "spaCy"]
            )
        ],
        full_text="这里是移动学习论文的完整文本...",
        extraction_date=datetime.now().isoformat()
    )
    
    # 论文5：虚拟现实教育应用
    paper5 = ExtractedPaperData(
        paper_info=PaperInfo(
            paper_id="VR001",
            title="虚拟现实技术在历史教育中的沉浸式体验设计",
            authors=["孙磊", "田雨", "邓强"],
            year=2022,
            venue="虚拟现实与教育",
            abstract="本研究探索了虚拟现实技术在历史教育中的应用，通过构建沉浸式的历史场景，让学习者能够身临其境地体验历史事件，提高历史学习的趣味性和有效性。",
            keywords=["虚拟现实", "历史教育", "沉浸式体验", "教育技术"],
            doi="10.1234/vr.2022.001"
        ),
        research_problems=[
            ResearchProblem(
                problem_id="P501",
                title="VR技术如何增强历史学习的沉浸感和理解度",
                description="传统的历史教学主要依靠文字和图片，学习者难以真正理解历史事件的背景和意义。如何利用VR技术创造真实的历史场景，让学习者获得沉浸式的学习体验，从而提高对历史知识的理解和记忆，是VR教育应用的关键问题。",
                keywords=["虚拟现实", "沉浸式学习", "历史教育"],
                domain="教育技术",
                sub_domain="虚拟现实教育",
                research_type="应用研究"
            )
        ],
        research_methods=[
            ResearchMethod(
                method_id="M501",
                name="3D建模与场景重建",
                description="使用3D建模技术重建历史场景，创造沉浸式的虚拟环境",
                category="技术方法",
                techniques=["3D建模", "纹理映射", "光照渲染", "交互设计"],
                data_types=["3D模型数据", "历史资料", "用户交互数据"],
                analysis_tools=["Unity3D", "Blender", "Oculus SDK"]
            )
        ],
        full_text="这里是虚拟现实教育论文的完整文本...",
        extraction_date=datetime.now().isoformat()
    )
    
    return [paper1, paper2, paper3, paper4, paper5]


def save_diverse_data():
    """保存多样化的测试数据"""
    # 确保数据目录存在
    os.makedirs("./data", exist_ok=True)
    
    # 创建数据管理器
    data_manager = DataManager("./data")
    
    # 创建并保存多样化数据
    diverse_papers = create_diverse_papers()
    
    for paper in diverse_papers:
        data_manager.save_paper_data(paper)
        print(f"已保存论文数据: {paper.paper_info.title}")
    
    print(f"共创建了 {len(diverse_papers)} 篇多样化论文数据")
    
    # 统计信息
    total_problems = sum(len(paper.research_problems) for paper in diverse_papers)
    total_methods = sum(len(paper.research_methods) for paper in diverse_papers)
    domains = set()
    for paper in diverse_papers:
        for problem in paper.research_problems:
            domains.add(problem.domain)
    
    print(f"总计：{total_problems} 个研究问题，{total_methods} 个研究方法")
    print(f"涉及领域：{', '.join(domains)}")


if __name__ == "__main__":
    save_diverse_data()
